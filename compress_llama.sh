#!/bin/bash

# SVD-LLM-S: Enhanced version with weight-scale aware whitening

MODEL_NAME="llama-7b"                   
COMP_RATIO=0.8                                     
BASE_MODEL_PATH="/root/lanyun-tmp/models/${MODEL_NAME}"  
OUTPUT_PATH="/root/lanyun-tmp/SVD-LLM/result"

# --- 定义新的路径结构 ---
# 白化数据路径
OUR_METHOD_PATH="${OUTPUT_PATH}/compmodel/${MODEL_NAME}/our-method"
PROFILING_MAT_FILE="${OUR_METHOD_PATH}/white-data.pt"

# SVD-LLM 压缩模型路径
SVD_LLM_PATH="${OUTPUT_PATH}/compmodel/${MODEL_NAME}/our-method"
WHITENED_MODEL_FILE="${SVD_LLM_PATH}/compress-${COMP_RATIO}.pt"

SCALING_ALPHA=2.0   # 你可以在这里调整缩放参数a

# --- 为本次实验创建所需目录 ---
mkdir -p "${OUR_METHOD_PATH}"
mkdir -p "${SVD_LLM_PATH}"

# 1. 检查最终的压缩模型是否存在，如果不存在则生成
echo "步骤1：检查是否存在已压缩模型：$WHITENED_MODEL_FILE"
if [ -f "$WHITENED_MODEL_FILE" ]; then
    echo "已找到压缩模型，跳过生成步骤。"
else
    echo "未找到压缩模型，开始生成流程..."

    # 根据 COMP_RATIO 动态计算剪枝率 (使用 awk 替代 bc 以提高兼容性)
    PRUNING_RATIO=$(awk "BEGIN {print 1 - ${COMP_RATIO}}")

    # 构造传递给Python脚本的参数
    ARGS="--model $BASE_MODEL_PATH --step 1 --ratio $PRUNING_RATIO --whitening_nsamples 256 --dataset wikitext2 --seed 3 --model_seq_len 2048 --run_low_resource --scaling_alpha ${SCALING_ALPHA}"
    
    # 检查白化数据文件
    if [ -f "$PROFILING_MAT_FILE" ]; then
        echo "已找到Profiling矩阵，将基于现有矩阵生成压缩模型..."
        python SVDLLM.py $ARGS \
            --profiling_mat_path "${PROFILING_MAT_FILE}" \
            --save_path "${SVD_LLM_PATH}" \
            --save_file_name "compress-${COMP_RATIO}.pt"

    else
        echo "未找到Profiling矩阵，将生成新矩阵并压缩模型..."
        python SVDLLM.py $ARGS \
            --save_path "${SVD_LLM_PATH}" \
            --save_profiling_path "${PROFILING_MAT_FILE}" \
            --save_file_name "compress-${COMP_RATIO}.pt"
    fi
fi

# 2. 测试白化处理后的压缩模型
echo "步骤2：评估白化后的模型：$WHITENED_MODEL_FILE"
python SVDLLM.py \
    --step 4 \
    --model_path "$WHITENED_MODEL_FILE" \
    --eval_batch_size 1


